'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  Plus,
  Trash2,
  Loader2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Search,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  ModalResponsive,
  ModalResponsiveContent,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveTrigger,
} from '@/components/ui/modal-responsive';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useAuthStore } from '@/store/auth/action';
import { useNamecheapStore } from '@/store/namecheap/action';
import { DomainCheckResult } from '@/store/namecheap/type';
import { useOrderStore } from '@/store/order/action';
import { useTemplateStore } from '@/store/template/action';

const createOrderSchema = z.object({
  line_code: z
    .string()
    .min(1, 'Line code is required')
    .refine(
      val => val === val.toLowerCase(),
      'Line code must be lowercase only'
    ),
  name: z
    .string()
    .min(1, 'Name is required')
    .refine(val => val === val.toLowerCase(), 'Name must be lowercase only'),
  description: z.string().optional(),
  template_id: z.number().min(1, 'Template is required'),
  duration: z.number().min(1, 'Duration is required'),
  order_domains: z
    .array(
      z
        .object({
          name: z
            .string()
            .min(1, 'Domain name is required')
            .refine(
              val => val === val.toLowerCase(),
              'Domain name must be lowercase only'
            ),
          nameserver_1: z.string().optional(),
          nameserver_2: z.string().optional(),
          domain_type: z.enum(['none', 'internal', 'external']),
          price: z.number().min(0, 'Price must be 0 or greater').optional(),
        })
        .refine(
          data => {
            // If domain_type is external, nameservers are required
            if (data.domain_type === 'external') {
              return data.nameserver_1 && data.nameserver_1.trim().length > 0;
            }
            return true;
          },
          {
            message: 'Nameserver 1 is required for external domains',
            path: ['nameserver_1'],
          }
        )
        .refine(
          data => {
            // If domain_type is external, nameserver_2 is also required
            if (data.domain_type === 'external') {
              return data.nameserver_2 && data.nameserver_2.trim().length > 0;
            }
            return true;
          },
          {
            message: 'Nameserver 2 is required for external domains',
            path: ['nameserver_2'],
          }
        )
    )
    .min(2, 'At least 2 domains are required'),
});

type CreateOrderForm = z.infer<typeof createOrderSchema>;

interface CreateOrderModalProps {
  children: React.ReactNode;
  onSuccess?: () => void;
}

export function CreateOrderModal({
  children,
  onSuccess,
}: CreateOrderModalProps) {
  const [open, setOpen] = useState(false);
  const [domainsChecked, setDomainsChecked] = useState(false);
  const { me } = useAuthStore();
  const { createOrder, creating } = useOrderStore();
  const {
    templates,
    fetchTemplates,
    loading: templatesLoading,
  } = useTemplateStore();
  const {
    checkDomains,
    domainCheckResults,
    loading: checkingDomains,
    error: domainCheckError,
    clearResults,
    clearError,
  } = useNamecheapStore();

  const form = useForm<CreateOrderForm>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      line_code: '',
      name: '',
      description: '',
      template_id: 0,
      duration: 1,
      order_domains: [
        {
          name: '',
          nameserver_1: '',
          nameserver_2: '',
          domain_type: 'none' as const,
          price: undefined,
        },
        {
          name: '',
          nameserver_1: '',
          nameserver_2: '',
          domain_type: 'none' as const,
          price: undefined,
        },
      ],
    },
  });

  const {
    fields: domainFields,
    append: appendDomain,
    remove: removeDomain,
  } = useFieldArray({
    control: form.control,
    name: 'order_domains',
  });

  useEffect(() => {
    if (open && templates.length === 0) {
      fetchTemplates();
    }
  }, [open, templates.length, fetchTemplates]);

  // Reset domain check state when modal opens/closes
  useEffect(() => {
    if (!open) {
      clearResults();
      clearError();
      setDomainsChecked(false);
    }
  }, [open, clearResults, clearError]);

  // Helper function to check if there are regular domains that need checking
  const hasRegularDomainsToCheck = (): boolean => {
    const allDomains = form.getValues('order_domains');
    return allDomains.some(
      d => d.domain_type === 'none' && d.name && d.name.trim().length > 0
    );
  };

  // Auto-mark as checked when no regular domains exist
  const orderDomains = form.watch('order_domains');
  const hasRegularDomains = hasRegularDomainsToCheck();

  useEffect(() => {
    if (open && !hasRegularDomains && !domainsChecked) {
      setDomainsChecked(true);
    }
  }, [open, orderDomains, domainsChecked, hasRegularDomains]);

  const generateOrderCode = (templateName: string, userId: number): string => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`;
    return `${templateName.toUpperCase()}-${userId}-${timestamp}`;
  };

  // Helper functions for domain availability
  const getDomainAvailability = (
    domainName: string
  ): DomainCheckResult | null => {
    return (
      domainCheckResults.find(result => result.Domain === domainName) || null
    );
  };

  const getUnavailableDomains = (): string[] => {
    const formDomains = form
      .getValues('order_domains')
      .map(d => d.name)
      .filter(Boolean);
    return formDomains.filter(domainName => {
      const result = getDomainAvailability(domainName);
      return result && result.Available === 'false';
    });
  };

  const handleCheckAvailability = async () => {
    const allDomains = form.getValues('order_domains');

    // Only check domains that are not internal or external
    const domainsToCheck = allDomains
      .filter(d => d.domain_type === 'none' && d.name)
      .map(d => d.name);

    if (domainsToCheck.length === 0) {
      // If no domains need checking, mark as checked
      setDomainsChecked(true);
      toast.success('Domain validation completed');
      return;
    }

    // Validate domain names format
    const invalidDomains = domainsToCheck.filter(
      domain => !domain.includes('.')
    );
    if (invalidDomains.length > 0) {
      toast.error('Please enter valid domain names (e.g., example.com)');
      return;
    }

    try {
      clearError();
      const response = await checkDomains(domainsToCheck);
      if (response?.status) {
        setDomainsChecked(true);
        toast.success('Domain availability checked successfully');
      } else {
        toast.error(response?.message || 'Failed to check domain availability');
      }
    } catch (error) {
      console.error('Failed to check domains:', error);
      toast.error('Failed to check domain availability');
    }
  };

  const onSubmit = async (data: CreateOrderForm) => {
    try {
      if (!me?.id) {
        toast.error('User information not available');
        return;
      }

      // Check if domains have been checked (only for domains that need checking)
      const domainsNeedingCheck = data.order_domains.filter(
        d => d.domain_type === 'none' && d.name
      );
      if (domainsNeedingCheck.length > 0 && !domainsChecked) {
        toast.error(
          'Please check domain availability before creating the order'
        );
        return;
      }

      // Check for unavailable domains (only for domains that were checked)
      const unavailableDomains = getUnavailableDomains();
      if (unavailableDomains.length > 0) {
        toast.error(
          `Cannot create order: The following domains are unavailable: ${unavailableDomains.join(', ')}`
        );
        return;
      }

      const selectedTemplate = templates.find(t => t.id === data.template_id);
      if (!selectedTemplate) {
        toast.error('Selected template not found');
        return;
      }

      const generatedCode = generateOrderCode(selectedTemplate.name, me.id);

      const orderData = {
        ...data,
        code: generatedCode,
        description: data.description || '',
        order_domains: data.order_domains.map(domain => ({
          name: domain.name,
          nameserver_1: domain.nameserver_1,
          nameserver_2: domain.nameserver_2,
          is_internal: domain.domain_type === 'internal',
          is_external: domain.domain_type === 'external',
          price: domain.price,
        })),
      };

      const response = await createOrder(orderData);
      if (response?.status) {
        toast.success('Order created successfully');
        form.reset();
        setOpen(false);
        onSuccess?.();
      } else {
        toast.error(response?.message || 'Failed to create order');
      }
    } catch (error) {
      console.error('Failed to create order:', error);
      toast.error('Failed to create order');
    }
  };

  const handleAddDomain = () => {
    appendDomain({
      name: '',
      nameserver_1: '',
      nameserver_2: '',
      domain_type: 'none' as const,
      price: undefined,
    });
    // Reset domain check state when domains are modified
    setDomainsChecked(false);
    clearResults();
  };

  const handleRemoveDomain = (index: number) => {
    if (domainFields.length > 2) {
      removeDomain(index);
      // Reset domain check state when domains are modified
      setDomainsChecked(false);
      clearResults();
    }
  };

  const formatPrice = (price: string, currency: string = 'USD'): string => {
    const numPrice = parseFloat(price);
    return isNaN(numPrice) ? 'N/A' : `$${numPrice.toFixed(2)} ${currency}`;
  };

  const renderDomainStatus = (domainName: string) => {
    if (!domainName || !domainsChecked) {
      return null;
    }

    const result = getDomainAvailability(domainName);
    if (!result) {
      return null;
    }

    const isAvailable = result.Available === 'true';
    const isPremium = result.IsPremiumName === 'true';

    // Determine price to display based on conditions
    let priceToShow = null;
    let currency = 'USD';

    if (isPremium && isAvailable) {
      // Premium domain that's available - use PremiumRegistrationPrice
      priceToShow = result.PremiumRegistrationPrice;
    } else if (!isPremium && isAvailable) {
      // Regular domain that's available - use Price[0].Price
      if (result.Price && result.Price.length > 0) {
        priceToShow = result.Price[0].Price;
        currency = result.Price[0].Currency;
      }
    }
    // If unavailable (regardless of premium status), don't show price

    return (
      <div className='mt-2'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            {isAvailable ? (
              <Badge
                variant='default'
                className='bg-green-100 text-green-800 border-green-200'
              >
                <CheckCircle className='h-3 w-3 mr-1' />
                Available
              </Badge>
            ) : (
              <Badge variant='destructive'>
                <XCircle className='h-3 w-3 mr-1' />
                Unavailable
              </Badge>
            )}

            {isPremium && (
              <Badge
                variant='secondary'
                className='bg-yellow-100 text-yellow-800 border-yellow-200'
              >
                <AlertCircle className='h-3 w-3 mr-1' />
                Premium
              </Badge>
            )}
          </div>

          {priceToShow && (
            <div className='text-sm font-medium text-muted-foreground'>
              {formatPrice(priceToShow, currency)}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <ModalResponsive open={open} onOpenChange={setOpen}>
      <ModalResponsiveTrigger asChild>{children}</ModalResponsiveTrigger>
      <ModalResponsiveContent className='max-w-2xl max-h-[90vh] overflow-y-auto'>
        <ModalResponsiveHeader>
          <ModalResponsiveTitle>Create New Order</ModalResponsiveTitle>
          <ModalResponsiveDescription>
            Fill in the details to create a new order.
          </ModalResponsiveDescription>
        </ModalResponsiveHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            <FormField
              control={form.control}
              name='line_code'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Line Code</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter line code' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter order name' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Enter order description'
                      className='min-h-[80px]'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='template_id'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Template</FormLabel>
                  <Select
                    onValueChange={value => field.onChange(parseInt(value, 10))}
                    value={field.value ? field.value.toString() : ''}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select a template' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {templatesLoading ? (
                        <SelectItem value='loading' disabled>
                          Loading templates...
                        </SelectItem>
                      ) : templates.length === 0 ? (
                        <SelectItem value='no-templates' disabled>
                          No templates available
                        </SelectItem>
                      ) : (
                        templates.map(template => (
                          <SelectItem
                            key={template.id}
                            value={template.id.toString()}
                          >
                            {template.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='duration'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Duration</FormLabel>
                  <Select
                    onValueChange={value => field.onChange(parseInt(value, 10))}
                    value={field.value ? field.value.toString() : ''}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select duration' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='1'>1 month</SelectItem>
                      <SelectItem value='3'>3 months</SelectItem>
                      <SelectItem value='6'>6 months</SelectItem>
                      <SelectItem value='12'>12 months</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <FormLabel>Order Domains</FormLabel>
                <div className='flex items-center gap-2'>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={handleCheckAvailability}
                    disabled={checkingDomains}
                    className='flex items-center gap-2'
                  >
                    {checkingDomains ? (
                      <>
                        <Loader2 className='h-4 w-4 animate-spin' />
                        Checking...
                      </>
                    ) : (
                      <>
                        <Search className='h-4 w-4' />
                        Check Availability
                      </>
                    )}
                  </Button>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={handleAddDomain}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4' />
                    Add Domain
                  </Button>
                </div>
              </div>

              {domainCheckError && (
                <Alert variant='destructive'>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>{domainCheckError}</AlertDescription>
                </Alert>
              )}

              {domainsChecked && getUnavailableDomains().length > 0 && (
                <Alert variant='destructive'>
                  <XCircle className='h-4 w-4' />
                  <AlertDescription>
                    The following domains are unavailable:{' '}
                    {getUnavailableDomains().join(', ')}. Please remove them or
                    choose different domains before creating the order.
                  </AlertDescription>
                </Alert>
              )}

              <div className='space-y-4'>
                {domainFields.map((field, index) => (
                  <div
                    key={field.id}
                    className='space-y-3 p-4 border rounded-lg bg-muted/30'
                  >
                    <div className='flex items-center justify-between'>
                      <h4 className='font-medium text-sm'>
                        Domain {index + 1}
                      </h4>
                      {domainFields.length > 2 && (
                        <Button
                          type='button'
                          variant='outline'
                          size='sm'
                          onClick={() => handleRemoveDomain(index)}
                          className='flex items-center gap-1 text-destructive hover:text-destructive'
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      )}
                    </div>

                    <div className='space-y-3'>
                      <FormField
                        control={form.control}
                        name={`order_domains.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Domain Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder='Enter domain name (e.g., example.com)'
                                className='font-mono'
                                {...field}
                                onChange={e => {
                                  field.onChange(e);
                                  // Reset domain check state when domain names are modified
                                  setDomainsChecked(false);
                                  clearResults();
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`order_domains.${index}.domain_type`}
                        render={({ field }) => (
                          <FormItem className='space-y-3'>
                            <FormLabel>Domain Type</FormLabel>
                            <FormControl>
                              <RadioGroup
                                onValueChange={value => {
                                  field.onChange(value);
                                  // Reset nameserver fields when changing domain type
                                  form.resetField(
                                    `order_domains.${index}.nameserver_1`
                                  );
                                  form.resetField(
                                    `order_domains.${index}.nameserver_2`
                                  );
                                  // Reset domain check state when domain type changes
                                  setDomainsChecked(false);
                                  clearResults();
                                }}
                                value={field.value}
                                className='flex flex-row gap-6'
                              >
                                <div className='flex items-center space-x-2'>
                                  <RadioGroupItem
                                    value='none'
                                    id={`none-${index}`}
                                  />
                                  <FormLabel htmlFor={`none-${index}`}>
                                    Regular Domain
                                  </FormLabel>
                                </div>
                                <div className='flex items-center space-x-2'>
                                  <RadioGroupItem
                                    value='internal'
                                    id={`internal-${index}`}
                                  />
                                  <FormLabel htmlFor={`internal-${index}`}>
                                    Internal
                                  </FormLabel>
                                </div>
                                <div className='flex items-center space-x-2'>
                                  <RadioGroupItem
                                    value='external'
                                    id={`external-${index}`}
                                  />
                                  <FormLabel htmlFor={`external-${index}`}>
                                    External
                                  </FormLabel>
                                </div>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                        <FormField
                          control={form.control}
                          name={`order_domains.${index}.nameserver_1`}
                          render={({ field }) => {
                            const domainType = form.watch(
                              `order_domains.${index}.domain_type`
                            );
                            const isDisabled = domainType !== 'external';
                            return (
                              <FormItem>
                                <FormLabel>
                                  Nameserver 1{' '}
                                  {domainType === 'external' && (
                                    <span className='text-red-500'>*</span>
                                  )}
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder='ns1.example.com'
                                    className='font-mono'
                                    disabled={isDisabled}
                                    {...field}
                                    value={isDisabled ? '' : field.value}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            );
                          }}
                        />

                        <FormField
                          control={form.control}
                          name={`order_domains.${index}.nameserver_2`}
                          render={({ field }) => {
                            const domainType = form.watch(
                              `order_domains.${index}.domain_type`
                            );
                            const isDisabled = domainType !== 'external';
                            return (
                              <FormItem>
                                <FormLabel>
                                  Nameserver 2{' '}
                                  {domainType === 'external' && (
                                    <span className='text-red-500'>*</span>
                                  )}
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder='ns2.example.com'
                                    className='font-mono'
                                    disabled={isDisabled}
                                    {...field}
                                    value={isDisabled ? '' : field.value}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            );
                          }}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name={`order_domains.${index}.price`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Price</FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                step='0.01'
                                min='0'
                                placeholder='0.00'
                                {...field}
                                onChange={e => {
                                  const value = e.target.value;
                                  field.onChange(value === '' ? undefined : parseFloat(value));
                                }}
                                value={field.value ?? ''}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {renderDomainStatus(
                      form.watch(`order_domains.${index}.name`)
                    )}
                  </div>
                ))}
              </div>
            </div>

            <ModalResponsiveFooter>
              <Button
                type='button'
                variant='outline'
                onClick={() => setOpen(false)}
                disabled={creating || checkingDomains}
              >
                Cancel
              </Button>
              <Button
                type='submit'
                disabled={
                  creating ||
                  checkingDomains ||
                  (!domainsChecked &&
                    form.getValues('order_domains').some(d => d.name))
                }
              >
                {creating ? (
                  <>
                    <Loader2 className='h-4 w-4 animate-spin mr-2' />
                    Creating...
                  </>
                ) : (
                  'Create Order'
                )}
              </Button>
            </ModalResponsiveFooter>
          </form>
        </Form>
      </ModalResponsiveContent>
    </ModalResponsive>
  );
}
