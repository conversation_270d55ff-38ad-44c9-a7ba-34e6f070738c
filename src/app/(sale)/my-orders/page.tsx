'use client';

import {
  ChevronDown,
  ChevronRight,
  Plus,
  Server,
  ExternalLink,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { CreateOrderModal } from '@/components/sale/my-orders/create-order-modal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { formatDuration } from '@/lib/utils';
import { useOrderStore } from '@/store/order/action';

function getStatusColor(isConfirmed: boolean) {
  if (isConfirmed) {
    return 'bg-green-100 text-green-800 hover:bg-green-100';
  } else {
    return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
  }
}

function getStatusText(isConfirmed: boolean) {
  return isConfirmed ? 'Confirmed' : 'Pending';
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

export default function MyOrdersPage() {
  const { myOrders, myOrdersLoading, fetchMyOrders } = useOrderStore();
  const [expandedOrders, setExpandedOrders] = useState<Set<number>>(new Set());
  const router = useRouter();

  const handleOrderClick = (orderId: number) => {
    router.push(`/my-orders/${orderId}`);
  };

  const toggleOrderExpansion = (orderId: number) => {
    setExpandedOrders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(orderId)) {
        newSet.delete(orderId);
      } else {
        newSet.add(orderId);
      }
      return newSet;
    });
  };

  useEffect(() => {
    // Fetch my orders using the store
    fetchMyOrders();
  }, [fetchMyOrders]);

  if (myOrdersLoading) {
    return (
      <div className='space-y-6 p-6'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Orders</h1>
          <p className='text-muted-foreground'>Loading orders...</p>
        </div>
      </div>
    );
  }

  const handleOrderCreated = () => {
    // Refresh the orders list after successful creation
    fetchMyOrders({ is_confirmed: false });
  };

  return (
    <div className='space-y-6 p-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Orders</h1>
          <p className='text-muted-foreground'>
            Track and manage your sales orders
          </p>
        </div>
        <CreateOrderModal onSuccess={handleOrderCreated}>
          <Button className='flex items-center gap-2'>
            <Plus className='h-4 w-4' />
            Create Order
          </Button>
        </CreateOrderModal>
      </div>

      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Orders</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{myOrders.length}</div>
            <p className='text-xs text-muted-foreground'>All orders</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Confirmed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {myOrders.filter(order => order.is_confirmed).length}
            </div>
            <p className='text-xs text-muted-foreground'>Confirmed orders</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Pending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {myOrders.filter(order => !order.is_confirmed).length}
            </div>
            <p className='text-xs text-muted-foreground'>
              Awaiting confirmation
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Orders</CardTitle>
        </CardHeader>
        <CardContent className='p-0'>
          <div className='overflow-x-auto'>
            <Table className='min-w-full'>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-[50px] min-w-[50px]'></TableHead>
                  <TableHead className='min-w-[120px] whitespace-nowrap'>
                    Line Code
                  </TableHead>
                  <TableHead className='min-w-[120px] whitespace-nowrap'>
                    Order Code
                  </TableHead>
                  <TableHead className='min-w-[150px] whitespace-nowrap'>
                    Name
                  </TableHead>
                  <TableHead className='min-w-[120px] whitespace-nowrap'>
                    Template
                  </TableHead>
                  <TableHead className='min-w-[140px] whitespace-nowrap'>
                    Sales Person
                  </TableHead>
                  <TableHead className='min-w-[100px] whitespace-nowrap'>
                    Status
                  </TableHead>
                  <TableHead className='min-w-[100px] whitespace-nowrap'>
                    Duration
                  </TableHead>
                  <TableHead className='min-w-[80px] whitespace-nowrap text-center'>
                    Domains
                  </TableHead>
                  <TableHead className='min-w-[80px] whitespace-nowrap text-center'>
                    Projects
                  </TableHead>
                  <TableHead className='min-w-[120px] whitespace-nowrap'>
                    Created Date
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {myOrders.map(order => (
                  <React.Fragment key={order.id}>
                    <TableRow className='hover:bg-muted/50'>
                      <TableCell
                        className='cursor-pointer'
                        onClick={() => toggleOrderExpansion(order.id)}
                      >
                        <div className='flex items-center justify-center'>
                          {expandedOrders.has(order.id) ? (
                            <ChevronDown className='h-4 w-4' />
                          ) : (
                            <ChevronRight className='h-4 w-4' />
                          )}
                        </div>
                      </TableCell>
                      <TableCell
                        className='cursor-pointer whitespace-nowrap'
                        onClick={() => handleOrderClick(order.id)}
                      >
                        <div
                          className='max-w-[120px] truncate font-mono text-sm'
                          title={order.line_code || 'No line code'}
                        >
                          {order.line_code || '-'}
                        </div>
                      </TableCell>
                      <TableCell
                        className='font-medium cursor-pointer whitespace-nowrap'
                        onClick={() => handleOrderClick(order.id)}
                      >
                        {order.code}
                      </TableCell>
                      <TableCell
                        className='cursor-pointer whitespace-nowrap'
                        onClick={() => handleOrderClick(order.id)}
                      >
                        <div
                          className='max-w-[150px] truncate'
                          title={order.name}
                        >
                          {order.name}
                        </div>
                      </TableCell>
                      <TableCell
                        className='cursor-pointer whitespace-nowrap'
                        onClick={() => handleOrderClick(order.id)}
                      >
                        <div
                          className='max-w-[120px] truncate'
                          title={order.template.name}
                        >
                          {order.template.name}
                        </div>
                      </TableCell>
                      <TableCell
                        className='cursor-pointer whitespace-nowrap'
                        onClick={() => handleOrderClick(order.id)}
                      >
                        <div
                          className='max-w-[140px] truncate'
                          title={order.user.name}
                        >
                          {order.user.name}
                        </div>
                      </TableCell>
                      <TableCell
                        className='cursor-pointer whitespace-nowrap'
                        onClick={() => handleOrderClick(order.id)}
                      >
                        <Badge className={getStatusColor(order.is_confirmed)}>
                          {getStatusText(order.is_confirmed)}
                        </Badge>
                      </TableCell>
                      <TableCell
                        className='cursor-pointer whitespace-nowrap'
                        onClick={() => handleOrderClick(order.id)}
                      >
                        {formatDuration(order.duration)}
                      </TableCell>
                      <TableCell
                        className='cursor-pointer text-center whitespace-nowrap'
                        onClick={() => handleOrderClick(order.id)}
                      >
                        <Badge variant='outline'>
                          {order.order_domains?.length || 0}
                        </Badge>
                      </TableCell>
                      <TableCell
                        className='cursor-pointer text-center whitespace-nowrap'
                        onClick={() => handleOrderClick(order.id)}
                      >
                        <Badge variant='outline'>
                          {order.projects?.length || 0}
                        </Badge>
                      </TableCell>
                      <TableCell
                        className='cursor-pointer whitespace-nowrap'
                        onClick={() => handleOrderClick(order.id)}
                      >
                        {formatDate(order.created_at)}
                      </TableCell>
                    </TableRow>
                    {expandedOrders.has(order.id) && (
                      <TableRow>
                        <TableCell colSpan={11} className='p-0'>
                          <div className='bg-muted/30 p-4 space-y-4 overflow-x-auto'>
                            {/* Order Information */}
                            <div>
                              <h4 className='font-medium mb-2'>
                                Order Information
                              </h4>
                              <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm min-w-0'>
                                <div className='min-w-0'>
                                  <span className='text-muted-foreground whitespace-nowrap'>
                                    Line Code:
                                  </span>
                                  <span className='ml-2 font-medium font-mono'>
                                    {order.line_code || 'Not specified'}
                                  </span>
                                </div>
                                <div className='min-w-0'>
                                  <span className='text-muted-foreground whitespace-nowrap'>
                                    Duration:
                                  </span>
                                  <span className='ml-2 font-medium'>
                                    {formatDuration(order.duration)}
                                  </span>
                                </div>
                                <div className='min-w-0'>
                                  <span className='text-muted-foreground whitespace-nowrap'>
                                    Status:
                                  </span>
                                  <span className='ml-2'>
                                    <Badge
                                      className={getStatusColor(
                                        order.is_confirmed
                                      )}
                                    >
                                      {getStatusText(order.is_confirmed)}
                                    </Badge>
                                  </span>
                                </div>
                                <div className='min-w-0'>
                                  <span className='text-muted-foreground whitespace-nowrap'>
                                    Template:
                                  </span>
                                  <span
                                    className='ml-2 font-medium truncate'
                                    title={order.template.name}
                                  >
                                    {order.template.name}
                                  </span>
                                </div>
                                <div className='min-w-0'>
                                  <span className='text-muted-foreground whitespace-nowrap'>
                                    Sales Person:
                                  </span>
                                  <span
                                    className='ml-2 font-medium truncate'
                                    title={order.user.name}
                                  >
                                    {order.user.name}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Order Domains */}
                            <div>
                              <h4 className='font-medium mb-2 flex items-center gap-2'>
                                <span>
                                  Order Domains (
                                  {order.order_domains?.length || 0})
                                </span>
                              </h4>
                              {order.order_domains &&
                              order.order_domains.length > 0 ? (
                                <div className='space-y-3'>
                                  {order.order_domains.map((domain, index) => (
                                    <div
                                      key={domain.id || index}
                                      className='bg-background p-3 rounded border space-y-2'
                                    >
                                      <div className='flex items-center justify-between'>
                                        <span className='font-mono text-sm font-medium'>
                                          {domain.name}
                                        </span>
                                        <Badge
                                          variant={
                                            domain.is_available
                                              ? 'default'
                                              : 'secondary'
                                          }
                                        >
                                          {domain.is_available
                                            ? 'Available'
                                            : 'Not Available'}
                                        </Badge>
                                      </div>

                                      {(domain.nameserver_1 ||
                                        domain.nameserver_2) && (
                                        <div className='text-xs text-muted-foreground space-y-1'>
                                          {domain.nameserver_1 && (
                                            <div>
                                              <span className='font-medium'>
                                                NS1:
                                              </span>{' '}
                                              <span className='font-mono'>
                                                {domain.nameserver_1}
                                              </span>
                                            </div>
                                          )}
                                          {domain.nameserver_2 && (
                                            <div>
                                              <span className='font-medium'>
                                                NS2:
                                              </span>{' '}
                                              <span className='font-mono'>
                                                {domain.nameserver_2}
                                              </span>
                                            </div>
                                          )}
                                        </div>
                                      )}

                                      {(domain.is_internal ||
                                        domain.is_external) && (
                                        <div className='flex gap-2'>
                                          {domain.is_internal && (
                                            <Badge
                                              variant='outline'
                                              className='text-xs'
                                            >
                                              Internal
                                            </Badge>
                                          )}
                                          {domain.is_external && (
                                            <Badge
                                              variant='outline'
                                              className='text-xs'
                                            >
                                              External
                                            </Badge>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <p className='text-muted-foreground text-sm'>
                                  No domains associated with this order.
                                </p>
                              )}
                            </div>

                            {/* Projects */}
                            <div>
                              <h4 className='font-medium mb-2 flex items-center gap-2'>
                                <Server className='h-4 w-4' />
                                <span>
                                  Projects ({order.projects?.length || 0})
                                </span>
                              </h4>
                              {order.projects && order.projects.length > 0 ? (
                                <div className='space-y-2'>
                                  {order.projects.map((project, index) => (
                                    <div
                                      key={project.id || index}
                                      className='bg-background p-3 rounded border space-y-2'
                                    >
                                      <div className='flex items-center justify-between'>
                                        <div className='flex items-center gap-2'>
                                          <Server className='h-3 w-3 text-muted-foreground' />
                                          <span className='font-medium text-sm'>
                                            {project.name}
                                          </span>
                                          <span className='text-xs text-muted-foreground'>
                                            ({project.slug})
                                          </span>
                                        </div>
                                        <div className='flex items-center gap-1'>
                                          <Badge
                                            variant={
                                              project.is_active
                                                ? 'default'
                                                : 'secondary'
                                            }
                                            className='text-xs'
                                          >
                                            {project.is_active
                                              ? 'Active'
                                              : 'Inactive'}
                                          </Badge>
                                          <Badge
                                            variant='outline'
                                            className='text-xs'
                                          >
                                            {project.type}
                                          </Badge>
                                        </div>
                                      </div>
                                      {project.ingress_spec &&
                                        project.ingress_spec.length > 0 && (
                                          <div className='space-y-1'>
                                            <p className='text-xs text-muted-foreground'>
                                              Ingress Specs:
                                            </p>
                                            {project.ingress_spec.map(
                                              (spec, specIndex) => (
                                                <div
                                                  key={spec.id || specIndex}
                                                  className='flex items-center justify-between bg-muted/50 p-1 rounded text-xs'
                                                >
                                                  <div className='flex items-center gap-1'>
                                                    <ExternalLink className='h-3 w-3 text-muted-foreground' />
                                                    <span className='font-mono'>
                                                      {spec.host}
                                                      {spec.path}
                                                    </span>
                                                  </div>
                                                  <span className='text-muted-foreground'>
                                                    :{spec.port}
                                                  </span>
                                                </div>
                                              )
                                            )}
                                          </div>
                                        )}
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <p className='text-muted-foreground text-sm'>
                                  No projects created from this order yet.
                                </p>
                              )}
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
