'use client';

import {
  ArrowLeft,
  Calendar,
  FileText,
  Globe,
  Plus,
  Trash2,
  Shield,
  Server,
  ExternalLink,
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect } from 'react';
import { Toaster } from 'sonner';

import { AddOrderDomainModal } from '@/components/sale/my-orders/add-order-domain-modal';
import { DeleteOrderDomainModal } from '@/components/sale/my-orders/delete-order-domain-modal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { formatDuration } from '@/lib/utils';
import { useAuthStore } from '@/store/auth/action';
import { useOrderStore } from '@/store/order/action';

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

export default function OrderDetailPage() {
  const router = useRouter();
  const params = useParams();
  const orderId = params.id as string;

  const { me } = useAuthStore();
  const { selectedOrder, orderLoading, fetchOrder } = useOrderStore();

  useEffect(() => {
    if (orderId) {
      fetchOrder(parseInt(orderId, 10));
    }
  }, [orderId, fetchOrder]);

  // Validation: Check if the current user owns this order
  const isOwner = selectedOrder && me && selectedOrder.user_id === me.id;

  // Function to refresh order data after domain operations
  const handleRefreshOrder = () => {
    if (orderId) {
      fetchOrder(parseInt(orderId, 10));
    }
  };

  if (orderLoading) {
    return (
      <div className='space-y-6 p-6'>
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' onClick={() => router.back()}>
            <ArrowLeft className='h-4 w-4' />
          </Button>
        </div>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Order Details</h1>
          <p className='text-muted-foreground'>Loading order information...</p>
        </div>
      </div>
    );
  }

  if (!orderLoading && !selectedOrder) {
    return (
      <div className='space-y-6 p-6'>
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' onClick={() => router.back()}>
            <ArrowLeft className='h-4 w-4' />
          </Button>
        </div>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Order Details</h1>
          <p className='text-red-500'>Error: Order not found</p>
        </div>
      </div>
    );
  }

  // Check if user is authorized to view this order
  if (!orderLoading && selectedOrder && !isOwner) {
    return (
      <div className='space-y-6 p-6'>
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' onClick={() => router.back()}>
            <ArrowLeft className='h-4 w-4' />
          </Button>
        </div>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Order Details</h1>
          <p className='text-red-500'>
            Error: You are not authorized to view this order
          </p>
        </div>
      </div>
    );
  }

  if (!selectedOrder) {
    return null; // This should not happen due to the check above, but satisfies TypeScript
  }

  return (
    <div className='space-y-6 p-6'>
      <Toaster />
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' onClick={() => router.back()}>
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>
              {selectedOrder.name}
            </h1>
            <div className='flex items-center gap-2 mt-1'>
              <Badge
                variant={selectedOrder.is_confirmed ? 'default' : 'secondary'}
                className='flex items-center gap-1'
              >
                {selectedOrder.is_confirmed ? (
                  <Shield className='h-3 w-3' />
                ) : (
                  <Globe className='h-3 w-3' />
                )}
                {selectedOrder.is_confirmed ? 'Confirmed' : 'Pending'}
              </Badge>
            </div>
            <p className='text-muted-foreground mt-1'>
              Order #{selectedOrder.code} • Created{' '}
              {formatDate(selectedOrder.created_at)}
            </p>
          </div>
        </div>
      </div>

      <div className='grid gap-6 md:grid-cols-2'>
        {/* Order Information */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <FileText className='h-5 w-5' />
              Order Information
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid gap-3'>
              <div className='flex items-center gap-3'>
                <div>
                  <p className='text-sm font-medium'>Line Code</p>
                  <p className='text-sm text-muted-foreground font-mono'>
                    {selectedOrder.line_code || 'Not specified'}
                  </p>
                </div>
              </div>

              <div className='flex items-center gap-3'>
                <div>
                  <p className='text-sm font-medium'>Order Code</p>
                  <p className='text-sm text-muted-foreground'>
                    {selectedOrder.code}
                  </p>
                </div>
              </div>

              <div className='flex items-center gap-3'>
                <div>
                  <p className='text-sm font-medium'>Description</p>
                  <p className='text-sm text-muted-foreground'>
                    {selectedOrder.description || 'No description provided'}
                  </p>
                </div>
              </div>

              <div className='flex items-center gap-3'>
                <div>
                  <p className='text-sm font-medium'>Template</p>
                  <div className='flex items-center gap-2'>
                    <Badge variant='outline'>
                      {selectedOrder.template.name}
                    </Badge>
                    <span className='text-xs text-muted-foreground'>
                      ({selectedOrder.template.slug})
                    </span>
                  </div>
                </div>
              </div>

              <div className='flex items-center gap-3'>
                <div>
                  <p className='text-sm font-medium'>Duration</p>
                  <p className='text-sm text-muted-foreground'>
                    {formatDuration(selectedOrder.duration)}
                  </p>
                </div>
              </div>

              <div className='flex items-center gap-3'>
                <div>
                  <p className='text-sm font-medium'>Last Updated</p>
                  <p className='text-sm text-muted-foreground'>
                    {formatDate(selectedOrder.updated_at)}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sales Information */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Globe className='h-5 w-5' />
              Sales Information
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid gap-3'>
              <div className='flex items-center gap-3'>
                <div>
                  <p className='text-sm font-medium'>Sales Person</p>
                  <p className='text-sm text-muted-foreground'>
                    {selectedOrder.user.name}
                  </p>
                </div>
              </div>

              <div className='flex items-center gap-3'>
                <div>
                  <p className='text-sm font-medium'>Email</p>
                  <p className='text-sm text-muted-foreground'>
                    {selectedOrder.user.email}
                  </p>
                </div>
              </div>

              <div className='flex items-center gap-3'>
                <div>
                  <p className='text-sm font-medium'>User Type</p>
                  <Badge variant='outline'>
                    {selectedOrder.user.user_type.name}
                  </Badge>
                </div>
              </div>

              <div className='flex items-center gap-3'>
                <div>
                  <p className='text-sm font-medium'>Template Status</p>
                  <Badge
                    variant={
                      selectedOrder.template.is_active ? 'default' : 'secondary'
                    }
                  >
                    {selectedOrder.template.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Domains */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='flex items-center gap-2'>
              <Globe className='h-5 w-5' />
              Order Domains ({selectedOrder.order_domains?.length || 0})
            </CardTitle>
            {isOwner && (
              <AddOrderDomainModal
                orderId={selectedOrder.id}
                onSuccess={handleRefreshOrder}
              >
                <Button size='sm' className='flex items-center gap-2'>
                  <Plus className='h-4 w-4' />
                  Add Domain
                </Button>
              </AddOrderDomainModal>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {!selectedOrder.order_domains ||
          selectedOrder.order_domains.length === 0 ? (
            <div className='flex flex-col items-center justify-center py-8 text-center'>
              <Globe className='h-12 w-12 text-muted-foreground mb-4' />
              <h3 className='text-lg font-semibold'>No domains</h3>
              <p className='text-muted-foreground mb-4'>
                {`This order doesn't have any domains associated with it.`}
              </p>
              {isOwner && (
                <AddOrderDomainModal
                  orderId={selectedOrder.id}
                  onSuccess={handleRefreshOrder}
                >
                  <Button
                    variant='outline'
                    size='sm'
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4' />
                    Add First Domain
                  </Button>
                </AddOrderDomainModal>
              )}
            </div>
          ) : (
            <div className='grid gap-4'>
              {selectedOrder.order_domains.map(domain => (
                <div
                  key={domain.id}
                  className='p-4 border rounded-lg space-y-3'
                >
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-3'>
                      <Globe className='h-4 w-4 text-muted-foreground' />
                      <div>
                        <p className='font-medium font-mono'>{domain.name}</p>
                        {domain.created_at && (
                          <p className='text-xs text-muted-foreground'>
                            Added {formatDate(domain.created_at)}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Badge
                        variant={domain.is_available ? 'default' : 'secondary'}
                        className='text-xs flex items-center gap-1'
                      >
                        {domain.is_available && <Shield className='h-3 w-3' />}
                        {domain.is_available ? 'Available' : 'Unavailable'}
                      </Badge>
                      {isOwner && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div>
                              <DeleteOrderDomainModal
                                domainId={domain.id}
                                domainName={domain.name}
                                isAvailable={domain.is_available}
                                onSuccess={handleRefreshOrder}
                              >
                                <Button
                                  variant='ghost'
                                  size='sm'
                                  className={`h-8 w-8 p-0 ${
                                    domain.is_available
                                      ? 'text-muted-foreground cursor-not-allowed'
                                      : 'text-destructive hover:text-destructive'
                                  }`}
                                  disabled={domain.is_available}
                                >
                                  <Trash2 className='h-4 w-4' />
                                </Button>
                              </DeleteOrderDomainModal>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            {domain.is_available
                              ? 'Cannot delete available domains'
                              : 'Delete domain'}
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </div>
                  </div>

                  {/* Nameserver Information */}
                  {(domain.nameserver_1 || domain.nameserver_2) && (
                    <div className='space-y-2'>
                      <h5 className='text-sm font-medium text-muted-foreground'>
                        Nameservers
                      </h5>
                      <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                        {domain.nameserver_1 && (
                          <div className='text-sm'>
                            <span className='font-medium'>NS1:</span>{' '}
                            <span className='font-mono'>
                              {domain.nameserver_1}
                            </span>
                          </div>
                        )}
                        {domain.nameserver_2 && (
                          <div className='text-sm'>
                            <span className='font-medium'>NS2:</span>{' '}
                            <span className='font-mono'>
                              {domain.nameserver_2}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Domain Type Flags */}
                  {(domain.is_internal || domain.is_external) && (
                    <div className='space-y-2'>
                      <h5 className='text-sm font-medium text-muted-foreground'>
                        Domain Type
                      </h5>
                      <div className='flex gap-2'>
                        {domain.is_internal && (
                          <Badge variant='outline' className='text-xs'>
                            Internal
                          </Badge>
                        )}
                        {domain.is_external && (
                          <Badge variant='outline' className='text-xs'>
                            External
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Projects */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Server className='h-5 w-5' />
            Projects ({selectedOrder.projects?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!selectedOrder.projects || selectedOrder.projects.length === 0 ? (
            <div className='text-center py-8'>
              <Server className='h-12 w-12 text-muted-foreground mb-4 mx-auto' />
              <h3 className='text-lg font-semibold mb-2'>No projects</h3>
              <p className='text-muted-foreground text-sm'>
                {`This order doesn't have any projects created yet.`}
              </p>
            </div>
          ) : (
            <div className='space-y-4'>
              {selectedOrder.projects.map(project => (
                <div
                  key={project.id}
                  className='border rounded-lg p-4 space-y-3'
                >
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-3'>
                      <Server className='h-4 w-4 text-muted-foreground' />
                      <div>
                        <p className='font-medium'>{project.name}</p>
                        <p className='text-sm text-muted-foreground'>
                          {project.slug}
                        </p>
                      </div>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Badge
                        variant={project.is_active ? 'default' : 'secondary'}
                        className='text-xs'
                      >
                        {project.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                      <Badge variant='outline' className='text-xs'>
                        {project.type}
                      </Badge>
                    </div>
                  </div>

                  {project.ingress_spec && project.ingress_spec.length > 0 && (
                    <div className='space-y-2'>
                      <p className='text-sm font-medium text-muted-foreground'>
                        Ingress Specifications:
                      </p>
                      <div className='space-y-2'>
                        {project.ingress_spec.map(spec => (
                          <div
                            key={spec.id}
                            className='flex items-center justify-between bg-muted/30 p-2 rounded text-sm'
                          >
                            <div className='flex items-center gap-2'>
                              <ExternalLink className='h-3 w-3 text-muted-foreground' />
                              <span className='font-mono'>
                                {spec.host}
                                {spec.path}
                              </span>
                            </div>
                            <Badge variant='outline' className='text-xs'>
                              Port {spec.port}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Calendar className='h-5 w-5' />
            Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid gap-4 md:grid-cols-2'>
            <div className='flex items-center gap-3'>
              <div>
                <p className='text-sm font-medium'>Created</p>
                <p className='text-sm text-muted-foreground'>
                  {formatDate(selectedOrder.created_at)}
                </p>
              </div>
            </div>
            <div className='flex items-center gap-3'>
              <div>
                <p className='text-sm font-medium'>Last Updated</p>
                <p className='text-sm text-muted-foreground'>
                  {formatDate(selectedOrder.updated_at)}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
